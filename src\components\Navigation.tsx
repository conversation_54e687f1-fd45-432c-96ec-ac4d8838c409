import { Button } from "@/components/ui/button";
import { Home, User, Plus, LogOut, Heart, Building, Menu, X, Shield } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useProfile } from "@/hooks/useProfile";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useState } from "react";
import { DarkModeToggle } from "@/components/DarkModeToggle";

const Navigation = () => {
  const { user, signOut } = useAuth();
  const { profile } = useProfile();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Check if user is admin
  const isAdmin = profile?.user_role === 'admin' || profile?.user_role === 'super_admin';

  const handleSignOut = async () => {
    const { error } = await signOut();
    setIsMobileMenuOpen(false); // Close mobile menu
    if (error) {
      toast({
        title: "Error signing out",
        description: error.message,
        variant: "destructive",
      });
    } else {
      toast({
        title: "Signed out",
        description: "You have been signed out successfully.",
      });
    }
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    setIsMobileMenuOpen(false); // Close mobile menu when navigating
  };

  return (
    <nav className="sticky top-0 z-50 w-full bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border">
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        {/* Logo */}
        <div 
          className="flex items-center space-x-2 cursor-pointer hover:opacity-80 transition-opacity"
          onClick={() => navigate('/')}
        >
          <Home className="h-6 w-6 sm:h-8 sm:w-8 text-primary" />
          <span className="text-lg sm:text-xl font-bold text-foreground">BomaHub</span>
        </div>
        
        {/* Desktop Navigation */}
        <div className="hidden lg:flex items-center space-x-6">
          <button onClick={() => navigate('/')} className="text-foreground hover:text-primary transition-colors">
            Browse Properties
          </button>
          {user && (
            <>
              <button onClick={() => navigate('/my-properties')} className="text-foreground hover:text-primary transition-colors flex items-center gap-1">
                <Building className="h-4 w-4" />
                My Properties
              </button>
            </>
          )}
        </div>

        {/* Desktop Right Side */}
        <div className="hidden md:flex items-center space-x-2">
          <DarkModeToggle />
          {user ? (
            <>
              <span className="hidden lg:inline text-sm text-muted-foreground truncate max-w-32">
                Welcome, {user.email}
              </span>
              <Button variant="ghost" size="sm" onClick={() => navigate('/profile')}>
                <User className="h-4 w-4 mr-2" />
                <span className="hidden lg:inline">Profile</span>
              </Button>
              <Button variant="ghost" size="sm" onClick={handleSignOut}>
                <LogOut className="h-4 w-4 mr-2" />
                <span className="hidden lg:inline">Sign Out</span>
              </Button>
            </>
          ) : (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => navigate('/auth')}
              title="For property owners and landlords only"
            >
              <User className="h-4 w-4 mr-2" />
              <span className="hidden lg:inline">Owner Sign In</span>
              <span className="lg:hidden">Sign In</span>
            </Button>
          )}
          <Button 
            size="sm" 
            className="bg-gradient-to-r from-primary to-primary-light hover:from-primary-dark hover:to-primary"
            onClick={() => navigate('/add-property')}
            title="List your property for rent"
          >
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">List Property</span>
            <span className="sm:hidden">List</span>
          </Button>
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden flex items-center space-x-2">
          <DarkModeToggle />
          <Button 
            size="sm" 
            className="bg-gradient-to-r from-primary to-primary-light hover:from-primary-dark hover:to-primary p-2"
            onClick={() => navigate('/add-property')}
            title="List your property for rent"
          >
            <Plus className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="p-2"
          >
            {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-background border-t border-border">
          <div className="container mx-auto px-4 py-4 space-y-3">
            <button 
              onClick={() => handleNavigation('/')} 
              className="w-full text-left text-foreground hover:text-primary transition-colors py-2 px-3 rounded-md hover:bg-muted"
            >
              Browse Properties
            </button>
            
            {user ? (
              <>
                <button 
                  onClick={() => handleNavigation('/my-properties')} 
                  className="w-full text-left text-foreground hover:text-primary transition-colors py-2 px-3 rounded-md hover:bg-muted flex items-center gap-2"
                >
                  <Building className="h-4 w-4" />
                  My Properties
                </button>
                <button 
                  onClick={() => handleNavigation('/profile')} 
                  className="w-full text-left text-foreground hover:text-primary transition-colors py-2 px-3 rounded-md hover:bg-muted flex items-center gap-2"
                >
                  <User className="h-4 w-4" />
                  Profile
                </button>
                <button 
                  onClick={handleSignOut}
                  className="w-full text-left text-foreground hover:text-primary transition-colors py-2 px-3 rounded-md hover:bg-muted flex items-center gap-2"
                >
                  <LogOut className="h-4 w-4" />
                  Sign Out
                </button>
                <div className="text-sm text-muted-foreground py-2 px-3 border-t border-border">
                  Signed in as: {user.email}
                </div>
              </>
            ) : (
              <button 
                onClick={() => handleNavigation('/auth')} 
                className="w-full text-left text-foreground hover:text-primary transition-colors py-2 px-3 rounded-md hover:bg-muted flex items-center gap-2"
              >
                <User className="h-4 w-4" />
                Owner Sign In
              </button>
            )}
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navigation;