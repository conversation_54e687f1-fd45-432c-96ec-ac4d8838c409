import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MapPin, Bed, Bath, Square, Heart } from "lucide-react";
import { PropertyWithImages } from "@/types/property";
import { useFavorites } from "@/hooks/useFavorites";
import { cn } from "@/lib/utils";

interface PropertyCardProps {
  property: PropertyWithImages;
  onViewDetails: (propertyId: string) => void;
}

const PropertyCard = ({ property, onViewDetails }: PropertyCardProps) => {
  const { isFavorite, toggleFavorite } = useFavorites();
  
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency', 
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(price);
  };

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 bg-gradient-to-b from-card to-card/50 h-full">
      <div className="relative overflow-hidden rounded-t-lg">
        <img
          src={property.property_images?.[0]?.image_url || "/placeholder.svg"}
          alt={property.title}
          className="w-full h-40 sm:h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div className="absolute top-2 right-2 flex gap-1 sm:gap-2">
          <Button
            variant="outline"
            size="sm"
            className={cn(
              "h-7 w-7 sm:h-8 sm:w-8 p-0 bg-background/80 backdrop-blur-sm",
              isFavorite(property.id) && "text-red-500"
            )}
            onClick={(e) => {
              e.stopPropagation();
              toggleFavorite(property.id);
            }}
          >
            <Heart className={cn("h-3 w-3 sm:h-4 sm:w-4", isFavorite(property.id) && "fill-current")} />
          </Button>
          {property.featured && (
            <Badge variant="outline" className="bg-background/80 backdrop-blur-sm text-xs">
              Featured
            </Badge>
          )}
        </div>
        <div className="absolute bottom-2 left-2 bg-black/50 backdrop-blur rounded-lg px-2 py-1">
          <span className="text-white font-semibold text-sm sm:text-lg">
            KES {property.rent.toLocaleString()}/mo
          </span>
        </div>
      </div>
      
      <CardContent className="p-3 sm:p-4 flex flex-col flex-grow">
        <h3 className="font-semibold text-base sm:text-lg mb-2 text-card-foreground line-clamp-2">
          {property.title}
        </h3>
        
        <div className="flex items-center text-muted-foreground mb-3">
          <MapPin className="h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0" />
          <span className="text-xs sm:text-sm truncate">{property.location}</span>
        </div>

        <div className="flex items-center justify-between mb-3 sm:mb-4">
          <div className="flex items-center space-x-2 sm:space-x-4 text-xs sm:text-sm text-muted-foreground">
            <div className="flex items-center">
              <Bed className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
              <span>{property.bedrooms}</span>
            </div>
            <div className="flex items-center">
              <Bath className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
              <span>{property.bathrooms}</span>
            </div>
            <div className="flex items-center">
              <Square className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
              <span className="hidden xs:inline">{property.area} sqft</span>
              <span className="xs:hidden">{property.area}</span>
            </div>
          </div>
        </div>

        <div className="flex flex-wrap gap-1 mb-3 sm:mb-4">
          {property.amenities?.slice(0, 2).map((amenity) => (
            <Badge key={amenity} variant="secondary" className="text-xs">
              {amenity}
            </Badge>
          ))}
          {property.amenities && property.amenities.length > 2 && (
            <Badge variant="secondary" className="text-xs">
              +{property.amenities.length - 2} more
            </Badge>
          )}
        </div>

        <div className="flex flex-col gap-2 mt-auto">
          <div className="flex items-center gap-2">
            <Button 
              onClick={() => onViewDetails(property.id)}
              className="flex-1 bg-gradient-to-r from-primary to-primary-light hover:from-primary-dark hover:to-primary text-sm"
              size="sm"
            >
              View Details
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                toggleFavorite(property.id);
              }}
              className={cn(
                "h-8 w-8 p-0",
                isFavorite(property.id) && "text-red-500"
              )}
            >
              <Heart 
                className={cn(
                  "h-4 w-4",
                  isFavorite(property.id) && "fill-current"
                )} 
              />
            </Button>
          </div>
          
          {/* Quick Contact Buttons */}
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="sm"
              className="flex-1 text-xs h-7"
              onClick={(e) => {
                e.stopPropagation();
                const landlordPhone = property.profiles?.phone || "+254700000000";
                window.open(`tel:${landlordPhone}`, '_self');
              }}
            >
              📞 Call
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex-1 text-xs h-7 bg-green-50 hover:bg-green-100 text-green-800 hover:text-green-900 dark:bg-green-900/20 dark:hover:bg-green-800/30 dark:text-green-300 dark:hover:text-green-200 dark:border-green-700"
              onClick={(e) => {
                e.stopPropagation();
                const landlordPhone = property.profiles?.phone || "+254700000000";
                const phone = landlordPhone.replace(/\D/g, ''); // Remove non-digits for WhatsApp
                const message = encodeURIComponent(`Hi! I'm interested in your property "${property.title}" at ${property.location}. Is it still available? I found this listing on BomaHub.`);
                window.open(`https://wa.me/${phone}?text=${message}`, '_blank');
              }}
            >
              💬 WhatsApp
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PropertyCard;