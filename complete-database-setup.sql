-- Complete Database Setup for BomaHub
-- This file combines all migrations into a single comprehensive setup
-- Run this script on a fresh Supabase database

-- ============================================================================
-- STEP 1: CREATE CORE TABLES
-- ============================================================================

-- Create profiles table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE,
  full_name TEXT,
  phone TEXT,
  -- User role and verification status
  user_role TEXT DEFAULT 'landlord' CHECK (user_role IN ('landlord', 'admin', 'super_admin')),
  verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'under_review', 'verified', 'rejected', 'suspended')),
  verification_submitted_at TIMESTAMP WITH TIME ZONE,
  verification_completed_at TIMESTAMP WITH TIME ZONE,
  verified_by UUID REFERENCES auth.users(id),
  -- Identity verification
  national_id TEXT,
  business_registration_number TEXT,
  tax_pin TEXT,
  -- Contact verification
  phone_verified BOOLEAN DEFAULT FALSE,
  email_verified BOOLEAN DEFAULT FALSE,
  phone_verification_code TEXT,
  phone_verification_expires_at TIMESTAMP WITH TIME ZONE,
  -- Additional landlord info
  business_name TEXT,
  business_address TEXT,
  years_in_business INTEGER,
  -- Trust and safety
  trust_score DECIMAL(3,2) DEFAULT 0.00 CHECK (trust_score >= 0 AND trust_score <= 5.00),
  total_properties INTEGER DEFAULT 0,
  successful_rentals INTEGER DEFAULT 0,
  -- Admin notes and flags
  admin_notes TEXT,
  is_flagged BOOLEAN DEFAULT FALSE,
  flagged_reason TEXT,
  flagged_at TIMESTAMP WITH TIME ZONE,
  flagged_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
  PRIMARY KEY (id)
);

-- Create verification documents table
CREATE TABLE IF NOT EXISTS public.verification_documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  document_type TEXT NOT NULL CHECK (document_type IN ('national_id', 'passport', 'business_registration', 'tax_certificate', 'property_title_deed', 'lease_agreement', 'utility_bill', 'bank_statement')),
  document_url TEXT NOT NULL,
  document_name TEXT NOT NULL,
  file_size INTEGER,
  mime_type TEXT,
  verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'approved', 'rejected')),
  reviewed_by UUID REFERENCES auth.users(id),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  rejection_reason TEXT,
  expiry_date DATE, -- For documents that expire
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create property verification table
CREATE TABLE IF NOT EXISTS public.property_verification (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  property_id UUID REFERENCES public.properties(id) ON DELETE CASCADE NOT NULL,
  verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'under_review', 'verified', 'rejected')),
  ownership_verified BOOLEAN DEFAULT FALSE,
  location_verified BOOLEAN DEFAULT FALSE,
  photos_verified BOOLEAN DEFAULT FALSE,
  verified_by UUID REFERENCES auth.users(id),
  verified_at TIMESTAMP WITH TIME ZONE,
  rejection_reason TEXT,
  admin_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create fraud reports table
CREATE TABLE IF NOT EXISTS public.fraud_reports (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  reported_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  reported_property_id UUID REFERENCES public.properties(id) ON DELETE CASCADE,
  reporter_email TEXT NOT NULL,
  reporter_phone TEXT,
  report_type TEXT NOT NULL CHECK (report_type IN ('fake_listing', 'fake_landlord', 'scam_attempt', 'false_information', 'other')),
  description TEXT NOT NULL,
  evidence_urls TEXT[], -- Array of URLs to evidence files
  status TEXT DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'resolved', 'dismissed')),
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  assigned_to UUID REFERENCES auth.users(id),
  resolution_notes TEXT,
  resolved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create verification audit log
CREATE TABLE IF NOT EXISTS public.verification_audit_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  action TEXT NOT NULL, -- 'document_uploaded', 'verification_approved', 'verification_rejected', etc.
  details JSONB,
  performed_by UUID REFERENCES auth.users(id),
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create properties table with all enhancements
CREATE TABLE IF NOT EXISTS public.properties (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  rent DECIMAL(10,2) NOT NULL,
  location TEXT NOT NULL,
  county TEXT NOT NULL,
  bedrooms INTEGER NOT NULL,
  bathrooms INTEGER NOT NULL,
  area DECIMAL(10,2) NOT NULL,
  amenities TEXT[] DEFAULT '{}',
  available BOOLEAN DEFAULT true,
  featured BOOLEAN DEFAULT false,
  -- Verification and approval status
  approval_status TEXT DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected', 'suspended')),
  approved_by UUID REFERENCES auth.users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  rejection_reason TEXT,
  requires_verification BOOLEAN DEFAULT TRUE,
  -- Location coordinates and structured address fields
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  formatted_address TEXT,
  neighborhood TEXT,
  city TEXT,
  -- Multi-unit property fields
  is_multi_unit BOOLEAN DEFAULT FALSE,
  building_name VARCHAR(255),
  total_units INTEGER DEFAULT 1,
  property_type VARCHAR(50) DEFAULT 'single_unit',
  -- Units data as JSONB
  units JSONB DEFAULT '[]'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create property_images table
CREATE TABLE IF NOT EXISTS public.property_images (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  property_id UUID REFERENCES public.properties(id) ON DELETE CASCADE NOT NULL,
  image_url TEXT NOT NULL,
  is_primary BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create favorites table for user favorites
CREATE TABLE IF NOT EXISTS public.favorites (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  property_id UUID REFERENCES public.properties(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
  UNIQUE(user_id, property_id)
);

-- ============================================================================
-- STEP 2: CREATE MULTI-UNIT PROPERTY TABLES
-- ============================================================================

-- Create property_units table
CREATE TABLE IF NOT EXISTS property_units (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
    unit_name VARCHAR(100), -- e.g., "Single Room A1", "Studio B2"
    room_type VARCHAR(50) NOT NULL, -- 'single_room', 'bedsitter', 'studio', '1_bedroom', etc.
    bedrooms INTEGER NOT NULL,
    bathrooms INTEGER NOT NULL,
    area DECIMAL,
    rent DECIMAL NOT NULL,
    deposit DECIMAL,
    is_available BOOLEAN DEFAULT TRUE,
    unit_amenities TEXT[],
    unit_description TEXT,
    floor_number INTEGER,
    unit_number VARCHAR(20),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create unit_images table
CREATE TABLE IF NOT EXISTS unit_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    unit_id UUID REFERENCES property_units(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create unit_inquiries table
CREATE TABLE IF NOT EXISTS unit_inquiries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    unit_id UUID REFERENCES property_units(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id),
    inquiry_type VARCHAR(20) DEFAULT 'viewing', -- 'viewing', 'booking', 'application'
    message TEXT,
    contact_phone VARCHAR(20),
    preferred_date DATE,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'confirmed', 'cancelled', 'completed'
    created_at TIMESTAMP DEFAULT NOW()
);

-- ============================================================================
-- STEP 3: CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Location-based indexes
CREATE INDEX IF NOT EXISTS idx_properties_coordinates ON public.properties(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_properties_location_search ON public.properties USING GIN (to_tsvector('english', location || ' ' || COALESCE(neighborhood, '') || ' ' || COALESCE(city, '') || ' ' || county));

-- Units column index
CREATE INDEX IF NOT EXISTS idx_properties_units ON public.properties USING GIN (units);

-- Multi-unit property indexes
CREATE INDEX IF NOT EXISTS idx_property_units_property_id ON property_units(property_id);
CREATE INDEX IF NOT EXISTS idx_property_units_room_type ON property_units(room_type);
CREATE INDEX IF NOT EXISTS idx_property_units_is_available ON property_units(is_available);
CREATE INDEX IF NOT EXISTS idx_property_units_rent ON property_units(rent);
CREATE INDEX IF NOT EXISTS idx_unit_images_unit_id ON unit_images(unit_id);
CREATE INDEX IF NOT EXISTS idx_unit_inquiries_unit_id ON unit_inquiries(unit_id);
CREATE INDEX IF NOT EXISTS idx_unit_inquiries_user_id ON unit_inquiries(user_id);

-- ============================================================================
-- STEP 4: ENABLE ROW LEVEL SECURITY
-- ============================================================================

ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.property_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE property_units ENABLE ROW LEVEL SECURITY;
ALTER TABLE unit_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE unit_inquiries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.verification_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.property_verification ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fraud_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.verification_audit_log ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 5: CREATE RLS POLICIES
-- ============================================================================

-- Drop existing policies first (in case of re-run)
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Properties are viewable by everyone" ON public.properties;
DROP POLICY IF EXISTS "Users can insert their own properties" ON public.properties;
DROP POLICY IF EXISTS "Users can update their own properties" ON public.properties;
DROP POLICY IF EXISTS "Users can delete their own properties" ON public.properties;
DROP POLICY IF EXISTS "Property images are viewable by everyone" ON public.property_images;
DROP POLICY IF EXISTS "Property owners can insert images" ON public.property_images;
DROP POLICY IF EXISTS "Property owners can update images" ON public.property_images;
DROP POLICY IF EXISTS "Property owners can delete images" ON public.property_images;
DROP POLICY IF EXISTS "Users can view their own favorites" ON public.favorites;
DROP POLICY IF EXISTS "Users can insert their own favorites" ON public.favorites;
DROP POLICY IF EXISTS "Users can delete their own favorites" ON public.favorites;
DROP POLICY IF EXISTS "Anyone can view property units" ON property_units;
DROP POLICY IF EXISTS "Property owners can manage their units" ON property_units;
DROP POLICY IF EXISTS "Anyone can view unit images" ON unit_images;
DROP POLICY IF EXISTS "Property owners can manage unit images" ON unit_images;
DROP POLICY IF EXISTS "Users can view their own inquiries" ON unit_inquiries;

-- Create policies for profiles
CREATE POLICY "Public profiles are viewable by everyone" ON public.profiles
  FOR SELECT USING (true);

CREATE POLICY "Users can insert their own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

-- Create policies for properties
CREATE POLICY "Properties are viewable by everyone" ON public.properties
  FOR SELECT USING (true);

CREATE POLICY "Users can insert their own properties" ON public.properties
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own properties" ON public.properties
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own properties" ON public.properties
  FOR DELETE USING (auth.uid() = user_id);

-- Create policies for property_images
CREATE POLICY "Property images are viewable by everyone" ON public.property_images
  FOR SELECT USING (true);

CREATE POLICY "Property owners can insert images" ON public.property_images
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.properties 
      WHERE id = property_id AND user_id = auth.uid()
    )
  );

CREATE POLICY "Property owners can update images" ON public.property_images
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.properties 
      WHERE id = property_id AND user_id = auth.uid()
    )
  );

CREATE POLICY "Property owners can delete images" ON public.property_images
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.properties 
      WHERE id = property_id AND user_id = auth.uid()
    )
  );

-- Create policies for favorites
CREATE POLICY "Users can view their own favorites" ON public.favorites
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own favorites" ON public.favorites
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own favorites" ON public.favorites
  FOR DELETE USING (auth.uid() = user_id);

-- Create policies for property units
CREATE POLICY "Anyone can view property units"
ON property_units FOR SELECT
USING (true);

CREATE POLICY "Property owners can manage their units"
ON property_units FOR ALL
USING (
    property_id IN (
        SELECT id FROM properties WHERE user_id = auth.uid()
    )
);

-- Create policies for unit images
CREATE POLICY "Anyone can view unit images"
ON unit_images FOR SELECT
USING (true);

CREATE POLICY "Property owners can manage unit images"
ON unit_images FOR ALL
USING (
    unit_id IN (
        SELECT pu.id FROM property_units pu
        JOIN properties p ON pu.property_id = p.id
        WHERE p.user_id = auth.uid()
    )
);

-- Create policies for unit inquiries
CREATE POLICY "Users can view their own inquiries"
ON unit_inquiries FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own inquiries"
ON unit_inquiries FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Property owners can view inquiries for their units"
ON unit_inquiries FOR SELECT
USING (
    unit_id IN (
        SELECT pu.id FROM property_units pu
        JOIN properties p ON pu.property_id = p.id
        WHERE p.user_id = auth.uid()
    )
);

-- Create policies for verification documents
CREATE POLICY "Users can view their own verification documents"
ON verification_documents FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own verification documents"
ON verification_documents FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own verification documents"
ON verification_documents FOR UPDATE
USING (user_id = auth.uid());

CREATE POLICY "Admins can view all verification documents"
ON verification_documents FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid()
        AND user_role IN ('admin', 'super_admin')
    )
);

CREATE POLICY "Admins can update verification documents"
ON verification_documents FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid()
        AND user_role IN ('admin', 'super_admin')
    )
);

-- Create policies for property verification
CREATE POLICY "Property owners can view their property verification"
ON property_verification FOR SELECT
USING (
    property_id IN (
        SELECT id FROM properties WHERE user_id = auth.uid()
    )
);

CREATE POLICY "Admins can manage all property verifications"
ON property_verification FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid()
        AND user_role IN ('admin', 'super_admin')
    )
);

-- Create policies for fraud reports
CREATE POLICY "Anyone can insert fraud reports"
ON fraud_reports FOR INSERT
WITH CHECK (true);

CREATE POLICY "Admins can view and manage fraud reports"
ON fraud_reports FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid()
        AND user_role IN ('admin', 'super_admin')
    )
);

-- Create policies for verification audit log
CREATE POLICY "Admins can view audit logs"
ON verification_audit_log FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid()
        AND user_role IN ('admin', 'super_admin')
    )
);

CREATE POLICY "System can insert audit logs"
ON verification_audit_log FOR INSERT
WITH CHECK (true);

-- ============================================================================
-- STEP 6: CREATE FUNCTIONS AND TRIGGERS
-- ============================================================================

-- Create function to automatically create profile on user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name, phone)
  VALUES (
    new.id, 
    COALESCE(new.raw_user_meta_data->>'full_name', ''),
    COALESCE(new.raw_user_meta_data->>'phone', '')
  )
  ON CONFLICT (id) DO UPDATE SET
    full_name = COALESCE(EXCLUDED.full_name, profiles.full_name),
    phone = COALESCE(EXCLUDED.phone, profiles.phone),
    updated_at = NOW();
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create profile on user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = TIMEZONE('utc'::text, NOW());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS handle_updated_at ON public.profiles;
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

DROP TRIGGER IF EXISTS handle_updated_at ON public.properties;
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.properties
  FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

DROP TRIGGER IF EXISTS handle_updated_at ON public.verification_documents;
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.verification_documents
  FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

DROP TRIGGER IF EXISTS handle_updated_at ON public.property_verification;
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.property_verification
  FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

DROP TRIGGER IF EXISTS handle_updated_at ON public.fraud_reports;
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.fraud_reports
  FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

-- Create function to log verification actions
CREATE OR REPLACE FUNCTION public.log_verification_action()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.verification_audit_log (
    user_id,
    action,
    details,
    performed_by
  ) VALUES (
    COALESCE(NEW.user_id, OLD.user_id),
    TG_OP || '_' || TG_TABLE_NAME,
    jsonb_build_object(
      'old', to_jsonb(OLD),
      'new', to_jsonb(NEW)
    ),
    auth.uid()
  );

  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for audit logging
DROP TRIGGER IF EXISTS log_verification_document_changes ON public.verification_documents;
CREATE TRIGGER log_verification_document_changes
  AFTER INSERT OR UPDATE OR DELETE ON public.verification_documents
  FOR EACH ROW EXECUTE PROCEDURE public.log_verification_action();

DROP TRIGGER IF EXISTS log_profile_verification_changes ON public.profiles;
CREATE TRIGGER log_profile_verification_changes
  AFTER UPDATE OF verification_status ON public.profiles
  FOR EACH ROW EXECUTE PROCEDURE public.log_verification_action();

-- ============================================================================
-- STEP 7: SETUP STORAGE
-- ============================================================================

-- Create storage bucket for property images
INSERT INTO storage.buckets (id, name, public)
VALUES ('property-images', 'property-images', true)
ON CONFLICT (id) DO NOTHING;

-- Create storage bucket for verification documents (private)
INSERT INTO storage.buckets (id, name, public)
VALUES ('verification-documents', 'verification-documents', false)
ON CONFLICT (id) DO NOTHING;

-- Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Drop existing storage policies
DROP POLICY IF EXISTS "Public can view property images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload property images" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own property images" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own property images" ON storage.objects;

-- Create storage policies for property images
CREATE POLICY "Public can view property images" ON storage.objects
  FOR SELECT USING (bucket_id = 'property-images');

CREATE POLICY "Authenticated users can upload property images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'property-images' 
    AND auth.role() = 'authenticated'
  );

CREATE POLICY "Users can update their own property images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'property-images' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own property images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'property-images'
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- Create storage policies for verification documents
CREATE POLICY "Users can upload their own verification documents" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'verification-documents'
    AND auth.role() = 'authenticated'
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can view their own verification documents" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'verification-documents'
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Admins can view all verification documents" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'verification-documents'
    AND EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND user_role IN ('admin', 'super_admin')
    )
  );

CREATE POLICY "Users can update their own verification documents" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'verification-documents'
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own verification documents" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'verification-documents'
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- ============================================================================
-- STEP 8: DATA CLEANUP AND FINAL SETUP
-- ============================================================================

-- Update existing properties to have an empty units array if they don't have one
UPDATE public.properties 
SET units = '[]'::jsonb 
WHERE units IS NULL;

-- Add comments for documentation
COMMENT ON COLUMN public.profiles.phone IS 'User phone number for contact purposes';
COMMENT ON COLUMN public.properties.units IS 'JSON array containing unit data for multi-unit properties';
COMMENT ON COLUMN public.properties.latitude IS 'Property latitude coordinate for mapping';
COMMENT ON COLUMN public.properties.longitude IS 'Property longitude coordinate for mapping';
COMMENT ON COLUMN public.properties.is_multi_unit IS 'Flag indicating if this property has multiple units';

-- ============================================================================
-- SETUP COMPLETE
-- ============================================================================

-- Display completion message
DO $$
BEGIN
    RAISE NOTICE 'Database setup completed successfully!';
    RAISE NOTICE 'All tables, indexes, policies, and functions have been created.';
    RAISE NOTICE 'You can now use the BomaHub application.';
END $$;