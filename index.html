<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON> Finder - Find Your Perfect Home</title>
    <meta name="description" content="Find and discover amazing properties with Boma Finder" />
    <meta name="author" content="Boma Finder" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/house-favicon.svg" />
    <link rel="alternate icon" href="/favicon.ico" />
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#f35627" media="(prefers-color-scheme: light)" />
    <meta name="theme-color" content="#0f0f17" media="(prefers-color-scheme: dark)" />
    <meta name="color-scheme" content="light dark" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Boma Finder" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="msapplication-TileColor" content="#f35627" />
    <meta name="msapplication-config" content="/browserconfig.xml" />
    
    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/AppImages/ios/180.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/AppImages/ios/152.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/AppImages/ios/180.png" />
    <link rel="apple-touch-icon" sizes="167x167" href="/AppImages/ios/167.png" />

    <meta property="og:title" content="Boma Finder - Find Your Perfect Home" />
    <meta property="og:description" content="Find and discover amazing properties with Boma Finder" />
    <meta property="og:type" content="website" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Boma Finder" />
    <meta name="twitter:description" content="Find and discover amazing properties with Boma Finder" />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
