const CACHE_NAME = 'boma-finder-v1';
const urlsToCache = [
  '/',
  '/house-favicon.svg',
  '/placeholder.svg',
  '/manifest.json'
];

// Add message event listener for communication with main thread
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

// Install event - cache resources
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Opened cache');
        // Cache each URL individually to handle failures gracefully
        return Promise.allSettled(
          urlsToCache.map(url => {
            const request = new Request(url, {credentials: 'same-origin'});
            return cache.add(request).catch(error => {
              console.log(`Failed to cache ${url}:`, error);
            });
          })
        );
      })
      .then(() => {
        console.log('Service worker installed and cache populated');
        // Force the waiting service worker to become the active service worker
        return self.skipWaiting();
      })
      .catch((error) => {
        console.log('Cache setup failed:', error);
      })
  );
});

// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', (event) => {
  // Skip caching for chrome-extension, chrome, and other unsupported schemes
  if (!event.request.url.startsWith('http')) {
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Cache hit - return response
        if (response) {
          return response;
        }

        return fetch(event.request).then((response) => {
          // Check if we received a valid response
          if (!response || response.status !== 200 || response.type !== 'basic') {
            return response;
          }

          // Only cache GET requests
          if (event.request.method !== 'GET') {
            return response;
          }

          // Clone the response
          const responseToCache = response.clone();

          caches.open(CACHE_NAME)
            .then((cache) => {
              // Additional check before caching
              if (event.request.url.startsWith('http')) {
                cache.put(event.request, responseToCache);
              }
            })
            .catch((error) => {
              console.log('Cache put failed:', error);
            });

          return response;
        });
      })
      .catch((error) => {
        console.log('Fetch failed:', error);
        // Return a fallback response for navigation requests
        if (event.request.mode === 'navigate') {
          return caches.match('/');
        }
        throw error;
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      // Take control of all clients immediately
      return self.clients.claim();
    })
  );
});

// Background sync for offline property submissions
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

function doBackgroundSync() {
  // Handle offline data sync when connection is restored
  console.log('Background sync triggered');
}

// Push notifications (optional for property alerts)
self.addEventListener('push', (event) => {
  const options = {
    body: event.data ? event.data.text() : 'New property available!',
    icon: '/house-favicon.svg',
    badge: '/house-favicon.svg',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'View Property',
        icon: '/house-favicon.svg'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/house-favicon.svg'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification('Boma Finder', options)
  );
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  if (event.action === 'explore') {
    // Open the app to the property
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});
