import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useToast } from './use-toast';
import { Profile } from '@/types/property';

interface ProfileFormData {
  full_name?: string;
  phone?: string;
}

export const useProfile = () => {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [isCreatingProfile, setIsCreatingProfile] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  const createProfile = useCallback(async (profileData?: ProfileFormData) => {
    if (!user || isCreatingProfile) return null;

    setIsCreatingProfile(true);
    try {
      // First check if profile already exists to avoid duplicates
      const { data: existingProfile, error: fetchError } = await supabase
        .from('profiles')
        .select('id')
        .eq('id', user.id)
        .maybeSingle(); // Use maybeSingle instead of single to avoid throwing on no rows

      // If there's an error other than "no rows", handle it
      if (fetchError && fetchError.code !== 'PGRST116') {
        console.error('Error checking existing profile:', fetchError);
        throw fetchError;
      }

      if (existingProfile) {
        console.log('Profile already exists, refetching data...');
        // Refetch the full profile data
        const { data: fullProfile, error: refetchError } = await supabase
          .from('profiles')
          .select('id, full_name, phone, created_at, updated_at')
          .eq('id', user.id)
          .single();
        
        if (refetchError) {
          console.error('Error refetching profile:', refetchError);
          throw refetchError;
        }
        
        if (fullProfile) {
          setProfile(fullProfile);
        }
        return existingProfile;
      }

      // Create new profile
      const profileToInsert = {
        id: user.id,
        full_name: profileData?.full_name || user.user_metadata?.full_name || '',
        phone: profileData?.phone || user.user_metadata?.phone || '',
      };

      console.log('Creating new profile with data:', profileToInsert);

      const { data, error } = await supabase
        .from('profiles')
        .insert([profileToInsert])
        .select('id, full_name, phone, created_at, updated_at')
        .single();

      if (error) {
        // Handle unique constraint violation (profile already exists)
        if (error.code === '23505') {
          console.log('Profile already exists (unique constraint), refetching...');
          const { data: fullProfile, error: refetchError } = await supabase
            .from('profiles')
            .select('id, full_name, phone, created_at, updated_at')
            .eq('id', user.id)
            .single();
          
          if (refetchError) {
            console.error('Error refetching after conflict:', refetchError);
            throw refetchError;
          }
          
          if (fullProfile) {
            setProfile(fullProfile);
          }
          return fullProfile;
        }
        
        console.error('Profile creation error:', error);
        throw error;
      }

      setProfile(data);
      toast({
        title: "Profile created",
        description: "Your profile has been created successfully",
      });

      return data;
    } catch (error) {
      console.error('Error creating profile:', error);
      toast({
        title: "Error creating profile",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
      return null;
    } finally {
      setIsCreatingProfile(false);
    }
  }, [user, toast, isCreatingProfile]);

  const fetchProfile = useCallback(async () => {
    if (!user) {
      setProfile(null);
      setLoading(false);
      return;
    }

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, phone, created_at, updated_at')
        .eq('id', user.id)
        .maybeSingle(); // Use maybeSingle to avoid throwing on no rows

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching profile:', error);
        throw error;
      }

      if (!data) {
        // Profile doesn't exist, create one
        console.log('Profile not found, creating new profile...');
        await createProfile();
        return;
      }

      setProfile(data);
    } catch (error) {
      console.error('Error fetching profile:', error);
      
      // Handle specific errors
      if (error instanceof Error) {
        if (error.message.includes('406')) {
          toast({
            title: "Database Access Error",
            description: "There's an issue with the database configuration. Please check your RLS policies.",
            variant: "destructive",
          });
        } else {
          toast({
            title: "Error fetching profile",
            description: error.message,
            variant: "destructive",
          });
        }
      }
    } finally {
      setLoading(false);
    }
  }, [user, toast, createProfile]);

  const updateProfile = async (updates: ProfileFormData) => {
    if (!user) return null;

    try {
      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id)
        .select('id, full_name, phone, created_at, updated_at')
        .single();

      if (error) throw error;

      setProfile(data);
      toast({
        title: "Profile updated",
        description: "Your profile has been updated successfully",
      });

      return data;
    } catch (error) {
      toast({
        title: "Error updating profile",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
      return null;
    }
  };

  const deleteProfile = async () => {
    if (!user) return false;

    try {
      const { error } = await supabase
        .from('profiles')
        .delete()
        .eq('id', user.id);

      if (error) throw error;

      setProfile(null);
      toast({
        title: "Profile deleted",
        description: "Your profile has been deleted",
      });

      return true;
    } catch (error) {
      toast({
        title: "Error deleting profile",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
      return false;
    }
  };

  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  return {
    profile,
    loading,
    isCreatingProfile,
    fetchProfile,
    createProfile,
    updateProfile,
    deleteProfile,
  };
};
