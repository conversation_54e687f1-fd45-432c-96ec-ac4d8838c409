import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, FileText, CheckCircle, XCircle, Clock, AlertTriangle } from 'lucide-react';
import { useVerification } from '@/hooks/useVerification';
import { DocumentType, VerificationDocument } from '@/types/verification';

interface DocumentUploadProps {
  onUploadComplete?: () => void;
}

const DOCUMENT_TYPES: { value: DocumentType; label: string; description: string; required: boolean }[] = [
  {
    value: 'national_id',
    label: 'National ID',
    description: 'Government-issued national identification card',
    required: true
  },
  {
    value: 'business_registration',
    label: 'Business Registration',
    description: 'Certificate of business registration',
    required: false
  },
  {
    value: 'tax_certificate',
    label: 'Tax Certificate',
    description: 'Tax compliance certificate or PIN certificate',
    required: false
  },
  {
    value: 'property_title_deed',
    label: 'Property Title Deed',
    description: 'Legal document proving property ownership',
    required: true
  },
  {
    value: 'utility_bill',
    label: 'Utility Bill',
    description: 'Recent utility bill for address verification',
    required: false
  },
  {
    value: 'bank_statement',
    label: 'Bank Statement',
    description: 'Recent bank statement for financial verification',
    required: false
  }
];

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'approved':
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case 'rejected':
      return <XCircle className="h-4 w-4 text-red-600" />;
    case 'pending':
      return <Clock className="h-4 w-4 text-yellow-600" />;
    default:
      return <FileText className="h-4 w-4 text-gray-600" />;
  }
};

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'approved':
      return <Badge variant="default" className="bg-green-100 text-green-800">Approved</Badge>;
    case 'rejected':
      return <Badge variant="destructive">Rejected</Badge>;
    case 'pending':
      return <Badge variant="secondary">Under Review</Badge>;
    default:
      return <Badge variant="outline">Unknown</Badge>;
  }
};

export const DocumentUpload: React.FC<DocumentUploadProps> = ({ onUploadComplete }) => {
  const { uploadVerificationDocument, verificationDocuments, loading } = useVerification();
  const [selectedDocumentType, setSelectedDocumentType] = useState<DocumentType>('national_id');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [expiryDate, setExpiryDate] = useState<string>('');
  const [dragOver, setDragOver] = useState(false);

  const handleFileSelect = (file: File) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      alert('Please select a valid file type (JPEG, PNG, or PDF)');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB');
      return;
    }

    setSelectedFile(file);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    const success = await uploadVerificationDocument({
      document_type: selectedDocumentType,
      file: selectedFile,
      expiry_date: expiryDate || undefined,
    });

    if (success) {
      setSelectedFile(null);
      setExpiryDate('');
      onUploadComplete?.();
    }
  };

  const uploadedDocuments = verificationDocuments.reduce((acc, doc) => {
    acc[doc.document_type] = doc;
    return acc;
  }, {} as Record<string, VerificationDocument>);

  const requiredDocuments = DOCUMENT_TYPES.filter(doc => doc.required);
  const allRequiredUploaded = requiredDocuments.every(doc => uploadedDocuments[doc.value]);

  return (
    <div className="space-y-6">
      {/* Upload Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload Verification Documents
          </CardTitle>
          <CardDescription>
            Upload the required documents to verify your identity and property ownership.
            All documents will be securely stored and reviewed by our verification team.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="document-type">Document Type</Label>
            <Select value={selectedDocumentType} onValueChange={(value) => setSelectedDocumentType(value as DocumentType)}>
              <SelectTrigger>
                <SelectValue placeholder="Select document type" />
              </SelectTrigger>
              <SelectContent>
                {DOCUMENT_TYPES.map((docType) => (
                  <SelectItem key={docType.value} value={docType.value}>
                    <div className="flex items-center gap-2">
                      <span>{docType.label}</span>
                      {docType.required && <Badge variant="secondary" className="text-xs">Required</Badge>}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">
              {DOCUMENT_TYPES.find(doc => doc.value === selectedDocumentType)?.description}
            </p>
          </div>

          {/* File Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              dragOver ? 'border-primary bg-primary/5' : 'border-gray-300'
            }`}
            onDrop={handleDrop}
            onDragOver={(e) => {
              e.preventDefault();
              setDragOver(true);
            }}
            onDragLeave={() => setDragOver(false)}
          >
            {selectedFile ? (
              <div className="space-y-2">
                <FileText className="h-8 w-8 mx-auto text-green-600" />
                <p className="font-medium">{selectedFile.name}</p>
                <p className="text-sm text-muted-foreground">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedFile(null)}
                >
                  Remove
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                <Upload className="h-8 w-8 mx-auto text-gray-400" />
                <p className="text-sm">
                  <span className="font-medium">Click to upload</span> or drag and drop
                </p>
                <p className="text-xs text-muted-foreground">
                  PNG, JPG, or PDF (max 5MB)
                </p>
                <Input
                  type="file"
                  accept="image/*,.pdf"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleFileSelect(file);
                  }}
                  className="hidden"
                  id="file-upload"
                />
                <Label htmlFor="file-upload" className="cursor-pointer">
                  <Button variant="outline" size="sm" asChild>
                    <span>Choose File</span>
                  </Button>
                </Label>
              </div>
            )}
          </div>

          {/* Expiry Date (for documents that expire) */}
          {['national_id', 'passport', 'tax_certificate'].includes(selectedDocumentType) && (
            <div className="space-y-2">
              <Label htmlFor="expiry-date">Document Expiry Date (Optional)</Label>
              <Input
                id="expiry-date"
                type="date"
                value={expiryDate}
                onChange={(e) => setExpiryDate(e.target.value)}
              />
            </div>
          )}

          <Button
            onClick={handleUpload}
            disabled={!selectedFile || loading}
            className="w-full"
          >
            {loading ? 'Uploading...' : 'Upload Document'}
          </Button>
        </CardContent>
      </Card>

      {/* Uploaded Documents Status */}
      <Card>
        <CardHeader>
          <CardTitle>Document Status</CardTitle>
          <CardDescription>
            Track the verification status of your uploaded documents
          </CardDescription>
        </CardHeader>
        <CardContent>
          {verificationDocuments.length === 0 ? (
            <p className="text-muted-foreground text-center py-4">
              No documents uploaded yet
            </p>
          ) : (
            <div className="space-y-3">
              {DOCUMENT_TYPES.map((docType) => {
                const uploadedDoc = uploadedDocuments[docType.value];
                return (
                  <div key={docType.value} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(uploadedDoc?.verification_status || 'not_uploaded')}
                      <div>
                        <p className="font-medium">{docType.label}</p>
                        {uploadedDoc && (
                          <p className="text-sm text-muted-foreground">
                            Uploaded: {new Date(uploadedDoc.created_at).toLocaleDateString()}
                          </p>
                        )}
                        {uploadedDoc?.rejection_reason && (
                          <p className="text-sm text-red-600">
                            Reason: {uploadedDoc.rejection_reason}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {docType.required && !uploadedDoc && (
                        <Badge variant="outline" className="text-red-600 border-red-600">
                          Required
                        </Badge>
                      )}
                      {uploadedDoc ? (
                        getStatusBadge(uploadedDoc.verification_status)
                      ) : (
                        <Badge variant="outline">Not Uploaded</Badge>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {/* Verification Progress */}
          {verificationDocuments.length > 0 && (
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                {allRequiredUploaded ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-yellow-600" />
                )}
                <span className="font-medium">
                  Verification Progress
                </span>
              </div>
              <p className="text-sm text-muted-foreground">
                {allRequiredUploaded
                  ? "All required documents uploaded. Your application is under review."
                  : `${requiredDocuments.length - requiredDocuments.filter(doc => uploadedDocuments[doc.value]).length} required document(s) remaining.`
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
