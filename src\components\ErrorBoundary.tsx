import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ 
          padding: '20px', 
          fontFamily: 'Arial, sans-serif',
          background: '#f8f9fa',
          border: '1px solid #dc3545',
          borderRadius: '8px',
          margin: '20px',
          color: '#721c24'
        }}>
          <h1>🚨 Application Error</h1>
          <p>Something went wrong with the Boma Finder app.</p>
          <details style={{ marginTop: '16px' }}>
            <summary>Error Details</summary>
            <pre style={{ 
              background: '#fff', 
              padding: '10px', 
              borderRadius: '4px',
              overflow: 'auto',
              marginTop: '8px'
            }}>
              {this.state.error?.stack || this.state.error?.message || 'Unknown error'}
            </pre>
          </details>
          <div style={{ marginTop: '16px' }}>
            <button 
              onClick={() => window.location.reload()}
              style={{
                background: '#007bff',
                color: 'white',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '4px',
                cursor: 'pointer',
                marginRight: '8px'
              }}
            >
              Reload Page
            </button>
            <button 
              onClick={() => this.setState({ hasError: false })}
              style={{
                background: '#6c757d',
                color: 'white',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Try Again
            </button>
          </div>
          <div style={{ marginTop: '16px', fontSize: '14px', color: '#6c757d' }}>
            <p><strong>Common solutions:</strong></p>
            <ul>
              <li>Check browser console (F12) for more details</li>
              <li>Clear browser cache and reload</li>
              <li>Check if .env file has correct Supabase credentials</li>
              <li>Try running: npm install && npm run dev</li>
            </ul>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
